#Sat Aug 02 14:52:24 CST 2025
com.android.rockchip.mediacodecnew.app-main-5\:/anim/tp_speed_dialog_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\tp_speed_dialog_enter.xml
com.android.rockchip.mediacodecnew.app-main-5\:/anim/tp_speed_dialog_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\tp_speed_dialog_exit.xml
com.android.rockchip.mediacodecnew.app-main-5\:/anim/tp_speed_dropdown_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\tp_speed_dropdown_enter.xml
com.android.rockchip.mediacodecnew.app-main-5\:/anim/tp_speed_dropdown_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\tp_speed_dropdown_exit.xml
com.android.rockchip.mediacodecnew.app-main-5\:/animator/tp_video_button_press.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\animator\\tp_video_button_press.xml
com.android.rockchip.mediacodecnew.app-main-5\:/animator/tp_video_button_release.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\animator\\tp_video_button_release.xml
com.android.rockchip.mediacodecnew.app-main-5\:/color/tp_video_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\tp_video_button_text_color.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/dialog_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_fast_forward_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fast_forward_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_fast_rewind_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fast_rewind_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_pause_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pause_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_play_arrow_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play_arrow_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_settings_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_skip_next_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_skip_next_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_skip_previous_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_skip_previous_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/ic_step_frame_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_step_frame_white_24.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_speed_dropdown_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_speed_dropdown_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_speed_item_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_speed_item_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_speed_item_selected_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_speed_item_selected_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_speed_menu_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_speed_menu_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_video_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_video_button_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_video_controls_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_video_controls_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_video_play_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_video_play_button_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_video_progress_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_video_progress_drawable.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_video_progress_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_video_progress_thumb.xml
com.android.rockchip.mediacodecnew.app-main-5\:/drawable/tp_video_settings_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_video_settings_button_background.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/activity_image_video_compare.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_image_video_compare.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/activity_main.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/activity_test_tp_texture_view.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_test_tp_texture_view.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/activity_tp_video_player_new.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_tp_video_player_new.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/decoder.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\decoder.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/dialog_image_format_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_image_format_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/dialog_smb_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_smb_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/dialog_tp_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_tp_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/dialog_tp_test.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_tp_test.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/encoder.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\encoder.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/fragment_tp_network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_tp_network_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/fragment_tp_smb_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_tp_smb_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/fragment_tp_tv_mode_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_tp_tv_mode_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/image_viewer.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\image_viewer.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/item_settings_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_settings_menu.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/media_browser.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\media_browser.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/media_browser_integrated.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\media_browser_integrated.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/media_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\media_item.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\network_settings.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/popup_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_menu.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/spinner_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_item.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/tp_speed_dropdown_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\tp_speed_dropdown_menu.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/tp_speed_selection_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\tp_speed_selection_dialog.xml
com.android.rockchip.mediacodecnew.app-main-5\:/layout/tp_video_player_controls.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\tp_video_player_controls.xml
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-anydpi/ic_launcher.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.android.rockchip.mediacodecnew.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.android.rockchip.mediacodecnew.app-main-5\:/xml/backup_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.android.rockchip.mediacodecnew.app-main-5\:/xml/data_extraction_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
