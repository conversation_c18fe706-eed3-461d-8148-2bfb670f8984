"-Xallow-no-source-files" "-classpath" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2669297b5f9b187f7bde2c3e445bffee\\transformed\\material-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f7a8295a8d3dd3268158199318b53d48\\transformed\\appcompat-resources-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1e5baf19b836d5c4808a1d8753ee12e1\\transformed\\appcompat-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\99f6e4e89b3befd2995dc9785ee2cbd9\\transformed\\glide-4.15.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a86270f7a19a804961bc5622bf8f14d0\\transformed\\viewpager2-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5da513b7548620198cf650e090331834\\transformed\\fragment-1.5.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ed9ce19e8224d5a0c1c7009334c13af5\\transformed\\activity-1.10.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\438193f54d20d64af16cc0684901dfa1\\transformed\\library-2.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e5d35f9059267f90984d4ce1aafabe0b\\transformed\\RTSP-Server-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e1ba6072cc6338b0c638e68613708282\\transformed\\recyclerview-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c80d0c7bd9aac3133b0b03690e1c8ed\\transformed\\drawerlayout-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ca70e0baf44a7e40939f67f60ebde84b\\transformed\\coordinatorlayout-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e485c0fb03392148ab1e8155b84348b\\transformed\\dynamicanimation-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8c942bfa5452d8e6ac7898dcb2fd1c27\\transformed\\transition-1.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fd001966b93efc052cf2f7fab74dd3fa\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\81b2405a27e16f23e9b4c124c8407d9c\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b0c7b61043422817a86c349a13a4856b\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40baea3290614e8b68c52508c8f58d4f\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4ed7d51bab1604c9c1bd0dfdfa77c05c\\transformed\\lifecycle-livedata-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\95f615f018c7093893a80b136983d9c2\\transformed\\lifecycle-livedata-core-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ec55a75ea590e3a71ed46a9484d11e6e\\transformed\\lifecycle-viewmodel-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.6.2\\10f354fdb64868baecd67128560c5a0d6312c495\\lifecycle-common-2.6.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a981e70d70d3d8abae531e0ad0de0574\\transformed\\lifecycle-runtime-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1498154eef4da7a9ee7629809fe29c3e\\transformed\\lifecycle-viewmodel-savedstate-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\de385ba5624bcfdc704edf9a6d21de36\\transformed\\core-ktx-1.13.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80b713ba28e88de9748d3b9a535f3ea0\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9eb0572d75a796c518dad629d60ed806\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1f62e439d4ab6c09f954803320818968\\transformed\\core-1.13.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2b8e771f1c3f8a00cfb9434b52c475ba\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\963de8514543d68d27282b08e63a88ac\\transformed\\annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d1bbcc7da3557c9a671ff116c23b4a4f\\transformed\\encoder-2.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\104fec206282b7761411343ef14e0fbd\\transformed\\rtmp-2.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f7adad8ebb81e0c803638d08128c3bbd\\transformed\\rtsp-2.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4fc8c3cee42364e18b2265ba307f7d69\\transformed\\srt-2.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5c2a442d5c1277087ff95994e6a7d4ad\\transformed\\common-2.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4a212a309ba78bca1a7f49a3a13700de\\transformed\\cardview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9db215a05c263bb4af444d18aaabae49\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b2cf10608e69d8eedce186cde563021\\transformed\\gifdecoder-4.15.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\91307963f40d3a5eb6ff0a5f7e9edcca\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\17494f6351f1adc404bbd5755d1b3991\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dc73c07cc75496478ee987aba843ced\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\202f432b06fabb9987d51b9bd31ea536\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\44860d91fc2f2b1bdecf36272b312b2b\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e69c70f1cb90139317c18ce834d3ecb9\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.8.1\\b8a16fe526014b7941c1debaccaf9c5153692dbb\\annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.3\\2b09627576f0989a436a00a4a54b55fa5026fb86\\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.3\\38d9cad3a0b03a10453b56577984bdeb48edeed5\\kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.8.10\\7c002ac41f547a82e81dfebd2a20577a738dbf3f\\kotlin-stdlib-jdk8-1.8.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.8.10\\cb726a23c808a850a43e7d6b9d1ba91b02fe9f05\\kotlin-stdlib-jdk7-1.8.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.22\\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\\kotlin-stdlib-1.9.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\00086898af72e06a3bc1d6cba87a0c04\\transformed\\constraintlayout-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\eu.agno3.jcifs\\jcifs-ng\\2.1.9\\789d5003a75ea6b4e6129c128b700ecb8ef6fe63\\jcifs-ng-2.1.9.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\21dcddd9ad30ced219b9baad635502e2\\transformed\\Android-TiffBitmapFactory-0.9.9.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d31f3aaca47734ee8a31893b58872b9a\\transformed\\tracing-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.resourceinspection\\resourceinspection-annotation\\1.0.1\\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\\resourceinspection-annotation-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.github.bumptech.glide\\disklrucache\\4.15.1\\e47ade981aefb5b975382750490d195fa569bbdf\\disklrucache-4.15.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.github.bumptech.glide\\annotations\\4.15.1\\d3721a4986a833e4ea71126489d12e08964b6f07\\annotations-4.15.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\baba4c552343f0f6882e8f1b6ff1e2f3\\transformed\\exifinterface-1.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.slf4j\\slf4j-api\\1.7.32\\cdcff33940d9f2de763bc41ea05a0be5941176c3\\slf4j-api-1.7.32.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.bouncycastle\\bcprov-jdk15on\\1.69\\91e1628251cf3ca90093ce9d0fe67e5b7dab3850\\bcprov-jdk15on-1.69.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\ui\\TpImageView.kt" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\browser\\ImageVideoCompareActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\browser\\ImageViewerActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\browser\\MediaAdapter.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\browser\\MediaBrowserActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\browser\\TpTextureView.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\browser\\TpVideoPlayerActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\MainActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\settings\\adapters\\SettingsMenuAdapter.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\settings\\fragments\\TpNetworkSettingsFragment.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\settings\\fragments\\TpSmbSettingsFragment.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\settings\\fragments\\TpTvSettingsFragment.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\settings\\models\\SettingsMenuItem.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\integrated\\settings\\TpSettingsDialog.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\dialogs\\NetworkSettingsDialog.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\dialogs\\SMBSettingsDialog.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\ImageViewerActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\MediaAdapter.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\MediaBrowserActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\VideoDecoderActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\android\\rockchip\\camera2\\separated\\VideoEncoderActivity.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\test\\isp\\TpTestDialog.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\ui\\internal\\TpCustomProgressBar.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\ui\\internal\\TpViewTransform.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\ui\\TpRoiView.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\ui\\TpTextureView.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\ui\\TpVideoPlayerView.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\utils\\TpFileManager.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\utils\\TpHdmiMonitor.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\utils\\TpNetworkMonitor.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\utils\\TpSambaClient.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\config\\RTSPConfig.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\encoder\\AudioEncoder.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\encoder\\ScreenVideoEncoder.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\service\\ProjectionData.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\service\\RTSPService.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\service\\RTSPServiceConnection.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\service\\RTSPStreamer.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\rtsp\\TpRtspManager.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\service\\StreamingSocketService.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\service\\TpStreamingService.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpCameraManager.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpCaptureImage.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpImageLoader.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpSerialManager.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpTvPreview.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpVideoDecoder.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\internal\\TpVideoEncoder.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\TpImageLoader.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\TpIspParam.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\TpVideoConfig.java" "C:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\src\\main\\java\\com\\touptek\\video\\TpVideoSystem.java"