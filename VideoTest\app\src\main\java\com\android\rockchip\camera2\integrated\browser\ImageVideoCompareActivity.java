package com.android.rockchip.camera2.integrated.browser;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Matrix;
import android.os.Bundle;
import android.util.Log;
import android.view.TextureView;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;


import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.touptek.ui.TpImageView;
import com.touptek.video.TpImageLoader;
import com.touptek.video.TpVideoSystem;
import com.touptek.video.TpVideoConfig;
import com.android.rockchip.mediacodecnew.R;

// Matrix和RectF不再需要，使用View缩放方案

import java.io.File;

/**
 * ImageVideoCompareActivity - 图片视频对比界面
 *
 * 提供左右分屏对比功能：
 * - 左侧：显示用户选择的图片
 * - 右侧：显示实时相机预览
 */
public class ImageVideoCompareActivity extends AppCompatActivity {
    private static final String TAG = "ImageVideoCompare";
    private static final int REQUEST_CAMERA_PERMISSION = 200;

    // UI组件
    private TpImageView imageView;
    private TpTextureView textureView;
    private TextView tvImageInfo;
    private TextView tvPreviewInfo;
    private Button btnBack;
    private Button btnSync;

    // 视频系统（简化模式）
    private TpVideoSystem videoSystem;

    // 图片路径
    private String imagePath;

    // Camera实际输出尺寸缓存
    private android.util.Size cameraActualOutputSize;

    // 同步控制变量
    private boolean isUpdating = false;
    private boolean isSyncEnabled = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_video_compare);

        Log.d(TAG, "🚀 ImageVideoCompareActivity onCreate 开始");

        // 获取传入的图片路径
        imagePath = getIntent().getStringExtra("image_path");
        if (imagePath == null) {
            Log.e(TAG, "未接收到图片路径");
            Toast.makeText(this, "图片路径错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        Log.d(TAG, "📁 图片路径: " + imagePath);

        // 初始化UI组件
        initViews();
        Log.d(TAG, "✅ UI组件初始化完成");

        // 加载图片
        loadImage();
        Log.d(TAG, "✅ 图片加载完成");

        // 设置同步监听器
        setupSyncListeners();
        Log.d(TAG, "✅ 同步监听器设置完成");

        // 检查相机权限并启动预览
        checkCameraPermissionAndStart();
        Log.d(TAG, "✅ ImageVideoCompareActivity onCreate 完成");
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        imageView = findViewById(R.id.image_view);
        textureView = findViewById(R.id.texture_view);
        tvImageInfo = findViewById(R.id.tv_image_info);
        tvPreviewInfo = findViewById(R.id.tv_preview_info);
        btnBack = findViewById(R.id.btn_back);
        btnSync = findViewById(R.id.btn_sync);

        // 设置返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 设置同步按钮
        btnSync.setOnClickListener(v -> {
            isSyncEnabled = !isSyncEnabled;
            updateSyncButtonState();
            String message = isSyncEnabled ? "已开启同步缩放" : "已关闭同步缩放";
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        });

        // 初始化同步按钮状态
        updateSyncButtonState();

        Log.d(TAG, "UI组件初始化完成");
    }

    /**
     * 设置同步监听器
     */
    private void setupSyncListeners() {
        // 监听图片视图的Matrix变化，同步到视频视图
        imageView.setMatrixChangeListener(new kotlin.jvm.functions.Function0<kotlin.Unit>() {
            @Override
            public kotlin.Unit invoke() {
                Log.d(TAG, "🔔 图片视图Matrix变化监听器被触发，isSyncEnabled=" + isSyncEnabled + ", isUpdating=" + isUpdating);
                if (isSyncEnabled && !isUpdating) {
                    Log.d(TAG, "🔄 开始从图片视图同步到视频视图");
                    syncFromImageToTexture();
                } else {
                    Log.d(TAG, "⏸️ 同步被跳过：isSyncEnabled=" + isSyncEnabled + ", isUpdating=" + isUpdating);
                }
                return kotlin.Unit.INSTANCE;
            }
        });

        // 监听视频视图的Matrix变化，同步到图片视图
        textureView.setOnMatrixChangeListener(new TpTextureView.OnMatrixChangeListener() {
            @Override
            public void onMatrixChanged() {
                Log.d(TAG, "🔔 视频视图Matrix变化监听器被触发，isSyncEnabled=" + isSyncEnabled + ", isUpdating=" + isUpdating);
                if (isSyncEnabled && !isUpdating) {
                    Log.d(TAG, "🔄 开始从视频视图同步到图片视图");
                    syncFromTextureToImage();
                } else {
                    Log.d(TAG, "⏸️ 同步被跳过：isSyncEnabled=" + isSyncEnabled + ", isUpdating=" + isUpdating);
                }
            }
        });

        Log.d(TAG, "✅ 同步监听器设置完成，isSyncEnabled=" + isSyncEnabled);
    }

    /**
     * 相对变换数据类
     */
    private static class RelativeTransform {
        final float relativeScale;
        final float relativeTransX;
        final float relativeTransY;

        RelativeTransform(float relativeScale, float relativeTransX, float relativeTransY) {
            this.relativeScale = relativeScale;
            this.relativeTransX = relativeTransX;
            this.relativeTransY = relativeTransY;
        }

        @Override
        public String toString() {
            return String.format("RelativeTransform{scale=%.3f, transX=%.1f, transY=%.1f}",
                               relativeScale, relativeTransX, relativeTransY);
        }
    }

    /**
     * 从TpImageView提取相对变换
     */
    private RelativeTransform extractImageViewTransform() {
        try {
            Matrix imageMatrix = imageView.getImageMatrix();
            float[] values = new float[9];
            imageMatrix.getValues(values);

            float currentScaleX = values[Matrix.MSCALE_X];
            float currentScaleY = values[Matrix.MSCALE_Y];
            float currentTransX = values[Matrix.MTRANS_X];
            float currentTransY = values[Matrix.MTRANS_Y];

            float baseScale = imageView.getBaseScale();
            float relativeScale = (currentScaleX + currentScaleY) / (2 * baseScale);

            // 计算基础居中位置
            float imageViewWidth = imageView.getWidth();
            float imageViewHeight = imageView.getHeight();
            float imageWidth = imageView.getDrawable() != null ? imageView.getDrawable().getIntrinsicWidth() : 1;
            float imageHeight = imageView.getDrawable() != null ? imageView.getDrawable().getIntrinsicHeight() : 1;

            float baseTransX = (imageViewWidth - imageWidth * baseScale) / 2f;
            float baseTransY = (imageViewHeight - imageHeight * baseScale) / 2f;

            float relativeTransX = currentTransX - baseTransX;
            float relativeTransY = currentTransY - baseTransY;

            return new RelativeTransform(relativeScale, relativeTransX, relativeTransY);
        } catch (Exception e) {
            Log.e(TAG, "提取图片视图变换失败", e);
            return new RelativeTransform(1.0f, 0f, 0f);
        }
    }

    /**
     * 从图片视图同步变换到视频视图
     */
    private void syncFromImageToTexture() {
        isUpdating = true;
        try {
            RelativeTransform transform = extractImageViewTransform();
            applyTransformToTextureView(transform);
            Log.d(TAG, "从图片视图同步: " + transform);
        } catch (Exception e) {
            Log.e(TAG, "同步到视频视图失败", e);
        } finally {
            isUpdating = false;
        }
    }

    /**
     * 从TpTextureView提取相对变换
     */
    private RelativeTransform extractTextureViewTransform() {
        try {
            Matrix textureMatrix = textureView.getCurrentMatrix();
            float[] values = new float[9];
            textureMatrix.getValues(values);

            // 获取基础Matrix的值
            textureView.resetToInitialState();
            Matrix baseMatrix = textureView.getCurrentMatrix();
            float[] baseValues = new float[9];
            baseMatrix.getValues(baseValues);

            // 恢复当前状态
            textureView.setTransform(textureMatrix);

            // 计算相对变换
            float relativeScaleX = values[Matrix.MSCALE_X] / baseValues[Matrix.MSCALE_X];
            float relativeScaleY = values[Matrix.MSCALE_Y] / baseValues[Matrix.MSCALE_Y];
            float relativeScale = (relativeScaleX + relativeScaleY) / 2f;

            float relativeTransX = values[Matrix.MTRANS_X] - baseValues[Matrix.MTRANS_X];
            float relativeTransY = values[Matrix.MTRANS_Y] - baseValues[Matrix.MTRANS_Y];

            return new RelativeTransform(relativeScale, relativeTransX, relativeTransY);
        } catch (Exception e) {
            Log.e(TAG, "提取视频视图变换失败", e);
            return new RelativeTransform(1.0f, 0f, 0f);
        }
    }

    /**
     * 从视频视图同步变换到图片视图
     */
    private void syncFromTextureToImage() {
        isUpdating = true;
        try {
            RelativeTransform transform = extractTextureViewTransform();
            applyTransformToImageView(transform);
            Log.d(TAG, "从视频视图同步: " + transform);
        } catch (Exception e) {
            Log.e(TAG, "同步到图片视图失败", e);
        } finally {
            isUpdating = false;
        }
    }

    /**
     * 将相对变换应用到TpImageView
     */
    private void applyTransformToImageView(RelativeTransform transform) {
        try {
            float baseScale = imageView.getBaseScale();
            float targetScale = baseScale * transform.relativeScale;

            Matrix newMatrix = new Matrix();
            newMatrix.postScale(targetScale, targetScale);

            // 计算基础居中位置
            float imageViewWidth = imageView.getWidth();
            float imageViewHeight = imageView.getHeight();
            float imageWidth = imageView.getDrawable() != null ? imageView.getDrawable().getIntrinsicWidth() : 1;
            float imageHeight = imageView.getDrawable() != null ? imageView.getDrawable().getIntrinsicHeight() : 1;

            float baseTransX = (imageViewWidth - imageWidth * targetScale) / 2f;
            float baseTransY = (imageViewHeight - imageHeight * targetScale) / 2f;

            newMatrix.postTranslate(
                baseTransX + transform.relativeTransX,
                baseTransY + transform.relativeTransY
            );

            imageView.setImageMatrix(newMatrix);

            Log.d(TAG, "应用变换到图片视图: " + transform + ", targetScale=" + targetScale);
        } catch (Exception e) {
            Log.e(TAG, "应用变换到图片视图失败", e);
        }
    }

    /**
     * 将相对变换应用到TpTextureView
     */
    private void applyTransformToTextureView(RelativeTransform transform) {
        try {
            // 重置到基础状态
            textureView.resetToInitialState();
            Matrix baseMatrix = textureView.getCurrentMatrix();

            // 记录基础状态
            float[] baseValues = new float[9];
            baseMatrix.getValues(baseValues);
            Log.d(TAG, "📐 基础Matrix: scale=(" + baseValues[Matrix.MSCALE_X] + "," + baseValues[Matrix.MSCALE_Y] +
                      "), trans=(" + baseValues[Matrix.MTRANS_X] + "," + baseValues[Matrix.MTRANS_Y] + ")");

            Matrix newMatrix = new Matrix(baseMatrix);

            // 应用相对缩放（以视图中心为焦点）
            if (Math.abs(transform.relativeScale - 1.0f) > 0.001f) {
                float centerX = textureView.getWidth() / 2f;
                float centerY = textureView.getHeight() / 2f;
                newMatrix.postScale(transform.relativeScale, transform.relativeScale, centerX, centerY);
                Log.d(TAG, "🔍 应用缩放: " + transform.relativeScale + " 焦点=(" + centerX + "," + centerY + ")");
            }

            // 应用相对平移
            if (Math.abs(transform.relativeTransX) > 0.1f || Math.abs(transform.relativeTransY) > 0.1f) {
                newMatrix.postTranslate(transform.relativeTransX, transform.relativeTransY);
                Log.d(TAG, "📍 应用平移: (" + transform.relativeTransX + "," + transform.relativeTransY + ")");
            }

            // 记录最终Matrix
            float[] finalValues = new float[9];
            newMatrix.getValues(finalValues);
            Log.d(TAG, "🎯 最终Matrix: scale=(" + finalValues[Matrix.MSCALE_X] + "," + finalValues[Matrix.MSCALE_Y] +
                      "), trans=(" + finalValues[Matrix.MTRANS_X] + "," + finalValues[Matrix.MTRANS_Y] + ")");

            // 直接应用到TextureView，避免触发监听器
            textureView.setTransform(newMatrix);
            textureView.invalidate(); // 强制刷新显示

            // 验证应用结果
            Matrix appliedMatrix = textureView.getCurrentMatrix();
            float[] appliedValues = new float[9];
            appliedMatrix.getValues(appliedValues);
            Log.d(TAG, "✅ 应用后Matrix: scale=(" + appliedValues[Matrix.MSCALE_X] + "," + appliedValues[Matrix.MSCALE_Y] +
                      "), trans=(" + appliedValues[Matrix.MTRANS_X] + "," + appliedValues[Matrix.MTRANS_Y] + ")");

            Log.d(TAG, "应用变换到视频视图: " + transform);
        } catch (Exception e) {
            Log.e(TAG, "应用变换到视频视图失败", e);
        }
    }

    /**
     * 更新同步按钮状态
     */
    private void updateSyncButtonState() {
        btnSync.setAlpha(isSyncEnabled ? 1.0f : 0.5f);
        btnSync.setText(isSyncEnabled ? "同步" : "同步");
    }

    /**
     * 加载选中的图片
     */
    private void loadImage() {
        try {
            // 使用TpImageLoader加载图片
            TpImageLoader.loadFullImage(imagePath, imageView);

            // 显示图片信息
            File imageFile = new File(imagePath);
            String fileName = imageFile.getName();
            tvImageInfo.setText(fileName);

            Log.d(TAG, "图片加载完成: " + fileName);

        } catch (Exception e) {
            Log.e(TAG, "加载图片失败", e);
            Toast.makeText(this, "加载图片失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 检查相机权限并启动预览
     */
    private void checkCameraPermissionAndStart() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        } else {
            requestCameraPermission();
        }
    }

    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                REQUEST_CAMERA_PERMISSION);
    }

    /**
     * 启动相机预览（使用TpVideoSystem）
     */
    private void startCameraPreview() {
        try {
            // 创建简化的视频配置
            TpVideoConfig config = TpVideoConfig.createDefault4K();
            videoSystem = new TpVideoSystem(this, config);

            // 设置监听器
            videoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
                @Override
                public void onCameraStarted() {
                    Log.d(TAG, "对比模式相机启动完成");
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("实时预览");

                        // 获取Camera输出尺寸并设置到TpTextureView
                        android.util.Size cameraSize = getCameraActualOutputSize();
                        textureView.setVideoSize(cameraSize.getWidth(), cameraSize.getHeight());

                        Log.d(TAG, "✅ TpTextureView已配置: " + cameraSize.getWidth() + "x" + cameraSize.getHeight());
                        Log.d(TAG, "🎯 所有手势操作现在与TpImageView完全一致");
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "对比模式相机错误: " + errorMessage);
                    runOnUiThread(() -> {
                        tvPreviewInfo.setText("预览错误");
                        Toast.makeText(ImageVideoCompareActivity.this,
                            "预览错误: " + errorMessage, Toast.LENGTH_SHORT).show();
                    });
                }
            });

            // 等待TextureView准备好后初始化
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            } else {
                textureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                    @Override
                    public void onSurfaceTextureAvailable(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture可用，TextureView尺寸: " + width + "x" + height);
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public void onSurfaceTextureSizeChanged(@NonNull android.graphics.SurfaceTexture surface, int width, int height) {
                        Log.d(TAG, "SurfaceTexture尺寸改变: " + width + "x" + height);
                        // 重新设置buffer size和变换
                        setupSurfaceTextureAndInit();
                    }

                    @Override
                    public boolean onSurfaceTextureDestroyed(@NonNull android.graphics.SurfaceTexture surface) {
                        return true;
                    }

                    @Override
                    public void onSurfaceTextureUpdated(@NonNull android.graphics.SurfaceTexture surface) {}
                });
            }

            Log.d(TAG, "TpVideoSystem预览已启动");

        } catch (Exception e) {
            Log.e(TAG, "启动TpVideoSystem预览失败", e);
            Toast.makeText(this, "启动预览失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 设置SurfaceTexture并初始化视频系统
     * 核心解决方案：设置SurfaceTexture buffer size为Camera输出尺寸，避免变形
     */
    private void setupSurfaceTextureAndInit() {
        if (textureView == null || textureView.getSurfaceTexture() == null) {
            Log.w(TAG, "TextureView或SurfaceTexture未准备好");
            return;
        }

        try {
            // 1. 获取Camera实际输出尺寸
            android.util.Size cameraOutputSize = getCameraActualOutputSize();
            if (cameraOutputSize == null) {
                Log.w(TAG, "无法获取Camera输出尺寸，使用默认初始化");
                initVideoSystem();
                return;
            }

            // 2. 设置SurfaceTexture buffer size为Camera输出尺寸（关键步骤）
            android.graphics.SurfaceTexture surfaceTexture = textureView.getSurfaceTexture();
            surfaceTexture.setDefaultBufferSize(cameraOutputSize.getWidth(), cameraOutputSize.getHeight());

            Log.d(TAG, "✅ SurfaceTexture buffer size已设置为: " + cameraOutputSize.getWidth() + "x" + cameraOutputSize.getHeight());
            Log.d(TAG, "📐 这将避免Camera输出在SurfaceTexture层面的变形");

            // 3. 初始化视频系统
            initVideoSystem();

        } catch (Exception e) {
            Log.e(TAG, "设置SurfaceTexture失败，使用默认初始化", e);
            initVideoSystem();
        }
    }

    /**
     * 初始化视频系统
     */
    private void initVideoSystem() {
        if (videoSystem != null && textureView.getSurfaceTexture() != null) {
            android.view.Surface surface = new android.view.Surface(textureView.getSurfaceTexture());
            videoSystem.initialize(surface);
        }
    }

    /**
     * 获取Camera实际输出尺寸
     * 这是解决变形问题的关键：获取真实的Camera stream输出尺寸
     */
    private android.util.Size getCameraActualOutputSize() {
        if (cameraActualOutputSize != null) {
            return cameraActualOutputSize;
        }

        try {
            if (videoSystem != null && videoSystem.getVideoConfig() != null) {
                // 获取配置的视频尺寸作为基础
                android.util.Size configSize = videoSystem.getVideoConfig().getSize();

                // 缓存结果
                cameraActualOutputSize = configSize;

                Log.d(TAG, "Camera实际输出尺寸: " + configSize.getWidth() + "x" + configSize.getHeight());
                return configSize;
            }
        } catch (Exception e) {
            Log.e(TAG, "获取Camera输出尺寸失败", e);
        }

        // 如果无法获取，返回4K默认尺寸
        cameraActualOutputSize = new android.util.Size(3840, 2160);
        Log.w(TAG, "使用默认Camera输出尺寸: 3840x2160");
        return cameraActualOutputSize;
    }









    /**
     * 停止相机预览
     */
    private void stopCameraPreview() {
        if (videoSystem != null) {
            videoSystem.release();
            videoSystem = null;
            Log.d(TAG, "TpVideoSystem预览已停止");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startCameraPreview();
            } else {
                Toast.makeText(this, "需要相机权限才能显示实时预览", Toast.LENGTH_LONG).show();
                tvPreviewInfo.setText("无相机权限");
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 如果视频系统已经初始化，重新启动
        if (videoSystem != null && ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            if (textureView.isAvailable()) {
                setupSurfaceTextureAndInit();
            }
        } else if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED) {
            startCameraPreview();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopCameraPreview();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopCameraPreview();
        Log.d(TAG, "Activity已销毁，资源已清理");
    }
}
