package com.android.rockchip.camera2.integrated.browser;


import android.content.Context;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.TextureView;
import android.view.ViewConfiguration;


/**
 * TpTextureView - 与TpImageView完全一致的可缩放TextureView
 * 
 * 提供与TpImageView完全相同的缩放和平移体验：
 * - 硬边界限制，无阻尼效果
 * - 智能双击缩放：始终回到能让整个视频完全显示在屏幕上的最小缩放比例
 * - 双指缩放时焦点稳定，无跳跃感
 * - 解决手势冲突问题
 * 
 * 主要功能：
 * - 双指缩放（焦点稳定）
 * - 单指平移（硬边界限制）
 * - 双击缩放切换
 * - 小视频自动居中
 * - 大视频边缘对齐
 * - 惯性滑动
 */
public class TpTextureView extends TextureView {
    private static final String TAG = "TpTextureView";

    // 缩放相关（修正为FIT_CENTER逻辑）
    private float currentScale = 1f;
    private float baseScale = 1f;        // 统一的基础缩放（用于手势计算）
    private float baseScaleX = 1f;       // X轴基础缩放
    private float baseScaleY = 1f;       // Y轴基础缩放
    private float dynamicMinScale = 0.3f; // 将在初始化时设置为baseScale
    private static final float MAX_SCALE = 5.0f;
    private static final float SCALE_SENSITIVITY = 3.0f; // 缩放敏感度增强
    
    // 缩放优化参数（与TpImageView一致）
    private static final float MIN_SCALE_SPAN = 10f; // 降低触发阈值
    private static final float CUSTOM_SCALE_THRESHOLD = 3f; // 自定义缩放触发阈值

    // 矩阵和手势（与TpImageView一致）
    private final Matrix matrix = new Matrix();
    private final Matrix savedMatrix = new Matrix();
    private final Matrix baseMatrix = new Matrix();
    private ScaleGestureDetector scaleDetector;
    private GestureDetector gestureDetector;
    
    // 状态标志（与TpImageView一致）
    private boolean isInitialized = false;
    private boolean isScaling = false;
    private boolean isCustomScaling = false; // 自定义缩放状态
    private boolean isZoomEnabled = true;
    
    // 视图和视频尺寸
    private int viewWidth = 0;
    private int viewHeight = 0;
    private int videoWidth = 0;
    private int videoHeight = 0;
    


    // 自定义缩放检测变量（与TpImageView一致）
    private float lastDistance = -1f;
    private float lastFocusX = 0f;
    private float lastFocusY = 0f;
    
    // 触摸相关
    private int touchSlop;

    // 监听器
    private OnSingleTapListener onSingleTapListener;
    private OnMatrixChangeListener matrixChangeListener;

    public interface OnSingleTapListener {
        void onSingleTap(MotionEvent event);
    }

    public interface OnMatrixChangeListener {
        void onMatrixChanged();
    }

    public TpTextureView(Context context) {
        this(context, null);
    }

    public TpTextureView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TpTextureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        touchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
        setupGestureDetectors();
        setupTouchListener();
        
        // 配置缩放检测器的敏感度
        configureScaleDetectorSensitivity();
        
        Log.d(TAG, "TpTextureView initialized");
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        viewWidth = w;
        viewHeight = h;
        Log.d(TAG, "View size changed: " + w + "x" + h);
        
        // 如果视频尺寸已设置，更新Matrix
        if (videoWidth > 0 && videoHeight > 0) {
            updateMatrix();
        }
    }

    /**
     * 设置视频尺寸（必须调用此方法才能正确工作）
     */
    public void setVideoSize(int width, int height) {
        this.videoWidth = width;
        this.videoHeight = height;
        Log.d(TAG, "Video size set: " + width + "x" + height);
        
        // 如果视图尺寸已知，立即更新Matrix
        if (viewWidth > 0 && viewHeight > 0) {
            post(this::updateMatrix);
        }
    }

    /**
     * 更新Matrix（修正为FIT_CENTER逻辑，解决视频压缩问题）
     */
    private void updateMatrix() {
        if (viewWidth <= 0 || viewHeight <= 0 || videoWidth <= 0 || videoHeight <= 0) {
            Log.w(TAG, "❌ Invalid dimensions: view=" + viewWidth + "x" + viewHeight +
                      ", video=" + videoWidth + "x" + videoHeight);
            return;
        }

        Log.d(TAG, "🔧 开始FIT_CENTER Matrix计算:");
        Log.d(TAG, "  TextureView尺寸: " + viewWidth + "x" + viewHeight);
        Log.d(TAG, "  视频尺寸: " + videoWidth + "x" + videoHeight);

        // 计算宽高比
        float textureViewRatio = (float) viewWidth / viewHeight;  // 容器比例 ≈ 1.0
        float videoRatio = (float) videoWidth / videoHeight;      // 视频比例 ≈ 1.78 (16:9)

        Log.d(TAG, "  TextureView比例: " + String.format("%.3f", textureViewRatio));
        Log.d(TAG, "  视频比例: " + String.format("%.3f", videoRatio));

        // 🎯 关键修正：使用正确的FIT_CENTER逻辑
        if (videoRatio > textureViewRatio) {
            // 视频更宽，需要在Y轴方向缩小来保持比例
            baseScaleX = 1.0f;  // X轴保持满宽度
            baseScaleY = textureViewRatio / videoRatio;  // Y轴缩小到正确比例
            Log.d(TAG, "  视频更宽，X轴=1.0，Y轴=" + String.format("%.3f", baseScaleY));
        } else {
            // 视频更高，需要在X轴方向缩小来保持比例
            baseScaleX = videoRatio / textureViewRatio;  // X轴缩小到正确比例
            baseScaleY = 1.0f;  // Y轴保持满高度
            Log.d(TAG, "  视频更高，X轴=" + String.format("%.3f", baseScaleX) + "，Y轴=1.0");
        }

        // 统一的baseScale用于手势计算（取较小值确保完整显示）
        baseScale = Math.min(baseScaleX, baseScaleY);
        dynamicMinScale = baseScale;
        currentScale = baseScale;

        Log.d(TAG, "  最终缩放: scaleX=" + baseScaleX + ", scaleY=" + baseScaleY);
        Log.d(TAG, "  统一baseScale=" + baseScale + " (用于手势计算)");

        // 设置基础Matrix（使用分离的X/Y轴缩放）
        baseMatrix.reset();
        baseMatrix.postScale(baseScaleX, baseScaleY);

        // 居中平移（基于缩放后的尺寸）
        float scaledWidth = viewWidth * baseScaleX;
        float scaledHeight = viewHeight * baseScaleY;
        float translateX = (viewWidth - scaledWidth) / 2f;
        float translateY = (viewHeight - scaledHeight) / 2f;

        baseMatrix.postTranslate(translateX, translateY);

        Log.d(TAG, "  居中平移: X=" + String.format("%.1f", translateX) +
                   ", Y=" + String.format("%.1f", translateY));

        // 初始化当前Matrix
        matrix.set(baseMatrix);
        updateTextureMatrix(matrix);

        isInitialized = true;

        // 验证最终效果
        float actualDisplayWidth = viewWidth * baseScaleX;
        float actualDisplayHeight = viewHeight * baseScaleY;
        float actualRatio = actualDisplayWidth / actualDisplayHeight;

        Log.d(TAG, "✅ Matrix初始化完成:");
        Log.d(TAG, "  实际显示尺寸: " + String.format("%.1f", actualDisplayWidth) +
                   "x" + String.format("%.1f", actualDisplayHeight));
        Log.d(TAG, "  实际显示比例: " + String.format("%.2f", actualRatio) + ":1");
        Log.d(TAG, "  目标比例: " + String.format("%.2f", videoRatio) + ":1 (16:9)");

        if (Math.abs(actualRatio - videoRatio) < 0.1f) {
            Log.d(TAG, "  ✅ 比例正确！视频将以16:9比例显示");
        } else {
            Log.w(TAG, "  ⚠️ 比例可能有偏差");
        }
    }

    /**
     * 应用Matrix变换到TextureView
     */
    private void updateTextureMatrix(Matrix newMatrix) {
        if (newMatrix == null) return;

        setTransform(newMatrix);
        invalidate(); // 强制刷新

        // 通知Matrix变化监听器
        if (matrixChangeListener != null) {
            matrixChangeListener.onMatrixChanged();
        }
    }

    /**
     * 设置手势检测器（与TpImageView完全一致）
     */
    private void setupGestureDetectors() {
        // 缩放手势检测器
        scaleDetector = new ScaleGestureDetector(getContext(), new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                Log.d(TAG, "🔍 Scale begin - span: " + detector.getCurrentSpan());
                isScaling = true;
                return true;
            }

            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (!isInitialized) {
                    Log.w(TAG, "⚠️ Scale attempted but not initialized");
                    return false;
                }

                float scaleFactor = detector.getScaleFactor();
                Log.d(TAG, "🔍 Raw scale factor: " + scaleFactor);
                
                // 增强缩放敏感度（与TpImageView完全一致）
                float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                float newScale = Math.max(dynamicMinScale, Math.min(currentScale * enhancedFactor, MAX_SCALE));

                if (newScale != currentScale) {
                    float actualFactor = newScale / currentScale;
                    currentScale = newScale;
                    matrix.postScale(actualFactor, actualFactor, detector.getFocusX(), detector.getFocusY());
                    updateTextureMatrix(matrix);
                    Log.d(TAG, "✅ Scale applied: " + currentScale + " (factor: " + actualFactor + ")");
                }
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                Log.d(TAG, "🔍 Scale end");
                isScaling = false;
                syncCurrentScale(); // 同步状态
            }
        });

        // 手势检测器（与TpImageView完全一致）
        gestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                Log.d(TAG, "🎯 Single tap confirmed at (" + e.getX() + ", " + e.getY() + ")");
                if (onSingleTapListener != null) {
                    onSingleTapListener.onSingleTap(e);
                }
                return true;
            }

            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                if (!isInitialized || isScaling) return false;

                // 检查是否在最小缩放状态（与TpImageView一致）
                if (isAtMinimumScale()) {
                    Log.d(TAG, "🚫 Dragging disabled at minimum scale");
                    return false;
                }

                matrix.postTranslate(-distanceX, -distanceY);
                updateTextureMatrix(matrix);
                return true;
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                // 移除惯性滑动动画，简化逻辑
                return false;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (!isInitialized) return false;

                // 获取真实的缩放状态并添加详细调试
                float realScale = getCurrentScaleFactor();
                Log.d(TAG, "🎯 双击前状态: currentScale=" + String.format("%.3f", currentScale) +
                           ", realScale=" + String.format("%.3f", realScale) +
                           ", baseScale=" + String.format("%.3f", baseScale));

                syncCurrentScale(); // 同步状态

                float targetScale = realScale > baseScale * 1.5f ?
                    baseScale : // 缩小到适配大小
                    Math.min(MAX_SCALE, baseScale * 3f); // 放大到3倍

                Log.d(TAG, "🎯 双击目标: " + String.format("%.3f", targetScale) +
                           ", 焦点=(" + String.format("%.1f", e.getX()) + "," + String.format("%.1f", e.getY()) + ")");

                instantScaleTo(targetScale, e.getX(), e.getY());
                return true;
            }

            @Override
            public void onLongPress(MotionEvent e) {
                Log.d(TAG, "🔒 Long press detected - ignoring");
            }
        });
    }

    /**
     * 配置缩放检测器敏感度（与TpImageView一致）
     */
    private void configureScaleDetectorSensitivity() {
        try {
            java.lang.reflect.Field spanSlop = ScaleGestureDetector.class.getDeclaredField("mSpanSlop");
            spanSlop.setAccessible(true);
            spanSlop.setInt(scaleDetector, (int) MIN_SCALE_SPAN);
            Log.d(TAG, "✅ ScaleDetector sensitivity configured: MIN_SCALE_SPAN=" + MIN_SCALE_SPAN);
        } catch (Exception e) {
            Log.w(TAG, "⚠️ Failed to configure ScaleDetector sensitivity", e);
        }
    }

    /**
     * 设置触摸监听器（与TpImageView完全一致）
     */
    private void setupTouchListener() {
        setOnTouchListener((v, event) -> {
            boolean handled = false;

            if (isZoomEnabled) {
                // 自定义小距离缩放检测（与TpImageView一致）
                handleCustomScale(event);

                // 优先处理缩放手势
                if (scaleDetector.onTouchEvent(event)) {
                    handled = true;
                }

                // 只有在非缩放状态下才处理其他手势
                if (!scaleDetector.isInProgress() && !isCustomScaling) {
                    if (gestureDetector.onTouchEvent(event)) {
                        handled = true;
                    }
                }

                switch (event.getActionMasked()) {
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        if (!isScaling && !isCustomScaling) {
                            instantCheckBounds();
                        }
                        // 触摸结束时同步状态
                        syncCurrentScale();
                        isCustomScaling = false;
                        lastDistance = -1f;
                        break;
                }
            }

            // 重要：如果事件被处理了，就不要调用 performClick()
            if (!handled) {
                performClick();
            }

            // 总是返回 true 来消费事件，避免传递给父视图
            return true;
        });
    }

    /**
     * 自定义缩放检测（与TpImageView完全一致）
     */
    private void handleCustomScale(MotionEvent event) {
        if (event.getPointerCount() != 2) {
            lastDistance = -1f;
            return;
        }

        float distance = getDistance(event);

        if (lastDistance > 0) {
            float deltaDistance = Math.abs(distance - lastDistance);

            if (deltaDistance > CUSTOM_SCALE_THRESHOLD) {
                isCustomScaling = true;

                float scaleFactor = distance / lastDistance;
                float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                float newScale = Math.max(dynamicMinScale, Math.min(currentScale * enhancedFactor, MAX_SCALE));

                if (newScale != currentScale) {
                    float actualFactor = newScale / currentScale;
                    currentScale = newScale;

                    float focusX = (event.getX(0) + event.getX(1)) / 2f;
                    float focusY = (event.getY(0) + event.getY(1)) / 2f;

                    matrix.postScale(actualFactor, actualFactor, focusX, focusY);
                    updateTextureMatrix(matrix);

                    Log.d(TAG, "🎯 Custom scale: " + currentScale + " (delta: " + deltaDistance + ")");
                }
            }
        }

        lastDistance = distance;
        lastFocusX = (event.getX(0) + event.getX(1)) / 2f;
        lastFocusY = (event.getY(0) + event.getY(1)) / 2f;
    }

    /**
     * 计算两点间距离
     */
    private float getDistance(MotionEvent event) {
        if (event.getPointerCount() < 2) return 0f;

        float dx = event.getX(0) - event.getX(1);
        float dy = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 获取当前真实缩放因子（修正为基于baseScale的计算）
     */
    private float getCurrentScaleFactor() {
        float[] values = new float[9];
        matrix.getValues(values);
        float scaleX = values[Matrix.MSCALE_X];
        float scaleY = values[Matrix.MSCALE_Y];

        // 计算相对于基础缩放的倍数
        float relativeScaleX = scaleX / baseScaleX;
        float relativeScaleY = scaleY / baseScaleY;

        // 取平均值作为统一的缩放因子
        float avgScale = (relativeScaleX + relativeScaleY) / 2f;

        return avgScale * baseScale;
    }

    /**
     * 同步当前缩放状态（修正状态同步逻辑）
     */
    private void syncCurrentScale() {
        if (!isInitialized) return;

        float realScale = getCurrentScaleFactor();
        if (Math.abs(realScale - currentScale) > 0.01f) {
            Log.d(TAG, "🔄 Syncing scale: " + currentScale + " -> " + realScale);
            currentScale = realScale;
        }
    }

    /**
     * 检查是否处于最小缩放状态（与TpImageView完全一致）
     */
    private boolean isAtMinimumScale() {
        float realScale = getCurrentScaleFactor();
        return realScale <= dynamicMinScale * 1.01f;
    }

    /**
     * 边界检查和自动调整（移除动画，直接修正）
     */
    private void instantCheckBounds() {
        if (!isInitialized) return;

        RectF rect = getDisplayRect();
        float deltaX = 0f;
        float deltaY = 0f;

        Log.d(TAG, "🔍 边界检查: 显示区域=" + String.format("%.1f,%.1f-%.1f,%.1f",
               rect.left, rect.top, rect.right, rect.bottom));

        // X轴边界检查
        if (rect.width() <= viewWidth) {
            // 内容宽度小于等于视图宽度，居中显示
            deltaX = (viewWidth - rect.width()) / 2f - rect.left;
        } else if (rect.left > 0) {
            // 内容左边界超出视图左边界
            deltaX = -rect.left;
        } else if (rect.right < viewWidth) {
            // 内容右边界超出视图右边界
            deltaX = viewWidth - rect.right;
        }

        // Y轴边界检查
        if (rect.height() <= viewHeight) {
            // 内容高度小于等于视图高度，居中显示
            deltaY = (viewHeight - rect.height()) / 2f - rect.top;
        } else if (rect.top > 0) {
            // 内容上边界超出视图上边界
            deltaY = -rect.top;
        } else if (rect.bottom < viewHeight) {
            // 内容下边界超出视图下边界
            deltaY = viewHeight - rect.bottom;
        }

        // 🔧 关键修正：直接应用位置修正，移除动画
        if (Math.abs(deltaX) > 1f || Math.abs(deltaY) > 1f) {
            Log.d(TAG, "🎯 直接修正位置: deltaX=" + String.format("%.1f", deltaX) +
                       ", deltaY=" + String.format("%.1f", deltaY));

            matrix.postTranslate(deltaX, deltaY);
            updateTextureMatrix(matrix);
            syncCurrentScale();
        } else {
            Log.d(TAG, "✅ 边界检查完成，无需修正");
            syncCurrentScale();
        }
    }





    /**
     * 瞬间缩放到指定比例（修正时序问题）
     */
    private void instantScaleTo(float targetScale, float focusX, float focusY) {
        if (!isInitialized) return;

        Log.d(TAG, "🎯 开始瞬间缩放: 从" + String.format("%.3f", currentScale) +
                   "到" + String.format("%.3f", targetScale));

        float scaleFactor = targetScale / currentScale;
        currentScale = targetScale;

        // 应用缩放变换
        matrix.postScale(scaleFactor, scaleFactor, focusX, focusY);
        updateTextureMatrix(matrix);

        // 🔧 关键修正：先同步状态，再延迟执行边界检查
        syncCurrentScale();
        post(() -> {
            Log.d(TAG, "🔍 延迟执行边界检查");
            instantCheckBounds();
        });

        Log.d(TAG, "✅ 瞬间缩放完成，边界检查已延迟执行");
    }

    /**
     * 获取显示区域（修正为基于TextureView的逻辑）
     */
    private RectF getDisplayRect() {
        // TextureView的显示区域是基于View尺寸，而不是视频尺寸
        RectF rect = new RectF(0f, 0f, viewWidth, viewHeight);
        matrix.mapRect(rect);
        return rect;
    }



    // ========== 公共API方法 ==========

    /**
     * 获取当前缩放比例
     */
    public float getCurrentScale() {
        return currentScale;
    }

    /**
     * 获取基础缩放比例
     */
    public float getBaseScale() {
        return baseScale;
    }

    /**
     * 设置是否启用缩放
     */
    public void setZoomEnabled(boolean enabled) {
        this.isZoomEnabled = enabled;
        Log.d(TAG, "Zoom enabled: " + enabled);
    }

    /**
     * 是否启用缩放
     */
    public boolean isZoomEnabled() {
        return isZoomEnabled;
    }

    /**
     * 设置单击监听器
     */
    public void setOnSingleTapListener(OnSingleTapListener listener) {
        this.onSingleTapListener = listener;
    }

    /**
     * 设置Matrix变化监听器
     */
    public void setOnMatrixChangeListener(OnMatrixChangeListener listener) {
        this.matrixChangeListener = listener;
    }

    /**
     * 重置到初始状态
     */
    public void resetToInitialState() {
        if (!isInitialized) return;

        currentScale = baseScale;
        matrix.set(baseMatrix);
        updateTextureMatrix(matrix);

        Log.d(TAG, "Reset to initial state");
    }

    /**
     * 获取当前Matrix的副本
     */
    public Matrix getCurrentMatrix() {
        return new Matrix(matrix);
    }

    @Override
    public boolean performClick() {
        return super.performClick();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        Log.d(TAG, "TpTextureView detached from window");
    }
}
