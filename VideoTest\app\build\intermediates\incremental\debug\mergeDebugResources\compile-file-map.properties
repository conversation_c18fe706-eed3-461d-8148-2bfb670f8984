#Sat Aug 02 15:02:28 CST 2025
com.android.rockchip.mediacodecnew.app-main-39\:/anim/tp_speed_dialog_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dialog_enter.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/anim/tp_speed_dialog_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dialog_exit.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/anim/tp_speed_dropdown_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dropdown_enter.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/anim/tp_speed_dropdown_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dropdown_exit.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/animator/tp_video_button_press.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\animator_tp_video_button_press.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/animator/tp_video_button_release.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\animator_tp_video_button_release.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/color/tp_video_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_tp_video_button_text_color.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/dialog_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_fast_forward_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fast_forward_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_fast_rewind_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fast_rewind_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_launcher_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_launcher_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_pause_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_play_arrow_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_arrow_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_settings_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_skip_next_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_next_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_skip_previous_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_previous_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/ic_step_frame_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_step_frame_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_speed_dropdown_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_dropdown_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_speed_item_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_item_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_speed_item_selected_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_item_selected_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_speed_menu_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_menu_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_video_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_button_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_video_controls_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_controls_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_video_play_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_play_button_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_video_progress_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_progress_drawable.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_video_progress_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_progress_thumb.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/drawable/tp_video_settings_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_settings_button_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/activity_image_video_compare.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_image_video_compare.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/activity_main.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/activity_test_tp_texture_view.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_test_tp_texture_view.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/activity_tp_video_player_new.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_tp_video_player_new.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/decoder.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_decoder.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/dialog_image_format_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_image_format_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/dialog_smb_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_smb_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/dialog_tp_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_tp_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/dialog_tp_test.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_tp_test.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/encoder.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_encoder.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/fragment_tp_network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tp_network_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/fragment_tp_smb_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tp_smb_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/fragment_tp_tv_mode_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tp_tv_mode_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/image_viewer.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_image_viewer.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/item_settings_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_settings_menu.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/media_browser.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_media_browser.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/media_browser_integrated.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_media_browser_integrated.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/media_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_media_item.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_network_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/popup_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_popup_menu.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/spinner_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_item.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/tp_speed_dropdown_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_tp_speed_dropdown_menu.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/tp_speed_selection_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_tp_speed_selection_dialog.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/layout/tp_video_player_controls.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_tp_video_player_controls.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-anydpi/ic_launcher.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-hdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-mdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-xhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-39\:/xml/backup_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.android.rockchip.mediacodecnew.app-main-39\:/xml/data_extraction_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/anim/tp_speed_dialog_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dialog_enter.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/anim/tp_speed_dialog_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dialog_exit.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/anim/tp_speed_dropdown_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dropdown_enter.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/anim/tp_speed_dropdown_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_tp_speed_dropdown_exit.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/animator/tp_video_button_press.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\animator_tp_video_button_press.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/animator/tp_video_button_release.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\animator_tp_video_button_release.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/color/tp_video_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_tp_video_button_text_color.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/dialog_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_fast_forward_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fast_forward_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_fast_rewind_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fast_rewind_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_launcher_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_launcher_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_pause_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_play_arrow_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_arrow_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_settings_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_skip_next_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_next_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_skip_previous_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_previous_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/ic_step_frame_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_step_frame_white_24.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_speed_dropdown_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_dropdown_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_speed_item_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_item_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_speed_item_selected_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_item_selected_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_speed_menu_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_speed_menu_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_video_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_button_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_video_controls_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_controls_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_video_play_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_play_button_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_video_progress_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_progress_drawable.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_video_progress_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_progress_thumb.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/drawable/tp_video_settings_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tp_video_settings_button_background.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/activity_image_video_compare.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_image_video_compare.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/activity_main.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/activity_tp_video_player_new.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_tp_video_player_new.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/decoder.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_decoder.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/dialog_image_format_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_image_format_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/dialog_smb_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_smb_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/dialog_tp_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_tp_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/dialog_tp_test.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_tp_test.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/encoder.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_encoder.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/fragment_tp_network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tp_network_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/fragment_tp_smb_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tp_smb_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/fragment_tp_tv_mode_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tp_tv_mode_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/image_viewer.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_image_viewer.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/item_settings_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_settings_menu.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/media_browser.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_media_browser.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/media_browser_integrated.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_media_browser_integrated.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/media_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_media_item.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_network_settings.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/popup_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_popup_menu.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/spinner_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_item.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/tp_speed_dropdown_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_tp_speed_dropdown_menu.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/tp_speed_selection_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_tp_speed_selection_dialog.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/layout/tp_video_player_controls.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_tp_video_player_controls.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-anydpi/ic_launcher.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-hdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-mdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-xhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.android.rockchip.mediacodecnew.app-main-40\:/xml/backup_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.android.rockchip.mediacodecnew.app-main-40\:/xml/data_extraction_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
