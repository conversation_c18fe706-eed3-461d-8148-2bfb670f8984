@echo off
echo ========================================
echo    Android 视频图片同步调试日志监控
echo ========================================
echo.
echo 正在启动logcat监控...
echo 过滤标签: ImageVideoCompareActivity, TpImageView, TpTextureView
echo 日志级别: DEBUG及以上
echo.
echo 按 Ctrl+C 停止监控
echo ========================================
echo.

REM 清除之前的日志缓冲区
adb logcat -c

REM 启动实时日志监控
adb logcat -v time ^
  ImageVideoCompareActivity:D ^
  TpImageView:D ^
  TpTextureView:D ^
  *:S
